<?php
/**
 * Admin management class
 *
 * @package VoxelMediaIntegrator
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class for managing admin interface
 */
class VMI_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Register settings
        add_action( 'admin_init', array( $this, 'register_settings' ) );

        // Add AJAX handler for dismissing notices
        add_action( 'wp_ajax_vmi_dismiss_field_notice', array( $this, 'dismiss_field_notice' ) );

        // Enqueue admin scripts and styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add main menu item
        add_menu_page(
            __( 'Voxel Media Integrator', 'voxel-media-integrator' ),
            __( 'Media Integrator', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-settings',
            array( $this, 'render_settings_page' ),
            'dashicons-format-gallery',
            30
        );

        // Add settings submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Settings', 'voxel-media-integrator' ),
            __( 'Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-settings',
            array( $this, 'render_settings_page' )
        );

        // Add Virtual Tours Dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Virtual Tours', 'voxel-media-integrator' ),
            __( 'Virtual Tours', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-virtual-tours-dashboard',
            array( $this, 'render_virtual_tours_dashboard_page' )
        );

        // Add All Virtual Tours submenu
        add_submenu_page(
            'vmi-settings',
            __( 'All Virtual Tours', 'voxel-media-integrator' ),
            __( 'All Tours', 'voxel-media-integrator' ),
            'manage_options',
            'edit.php?post_type=vmi_virtual_tours',
            null
        );

        // Add New Virtual Tour submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Add New Virtual Tour', 'voxel-media-integrator' ),
            __( 'Add New Tour', 'voxel-media-integrator' ),
            'manage_options',
            'post-new.php?post_type=vmi_virtual_tours',
            null
        );

        // Add Tour Categories submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Tour Categories', 'voxel-media-integrator' ),
            __( 'Tour Categories', 'voxel-media-integrator' ),
            'manage_options',
            'edit-tags.php?taxonomy=vmi_media_category&post_type=vmi_virtual_tours',
            null
        );

        // Add Virtual Tour Settings submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Virtual Tour Settings', 'voxel-media-integrator' ),
            __( 'Tour Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-virtual-tours-settings',
            array( $this, 'render_virtual_tours_settings_page' )
        );

        // Add 3D models settings submenu
        add_submenu_page(
            'vmi-settings',
            __( '3D Models', 'voxel-media-integrator' ),
            __( '3D Models', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-3d-models',
            array( $this, 'render_3d_models_page' )
        );

        // Add LMS Dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'LMS Dashboard', 'voxel-media-integrator' ),
            __( 'LMS Dashboard', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-lms',
            array( $this, 'render_lms_page' )
        );

        // Add Courses submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Courses', 'voxel-media-integrator' ),
            __( 'Courses', 'voxel-media-integrator' ),
            'manage_options',
            'edit.php?post_type=vmi_courses',
            null
        );

        // Add New Course submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Add New Course', 'voxel-media-integrator' ),
            __( 'Add New Course', 'voxel-media-integrator' ),
            'manage_options',
            'post-new.php?post_type=vmi_courses',
            null
        );

        // Add Categories submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Course Categories', 'voxel-media-integrator' ),
            __( 'Course Categories', 'voxel-media-integrator' ),
            'manage_options',
            'edit-tags.php?taxonomy=vmi_course_category&post_type=vmi_courses',
            null
        );

        // Add Lessons submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Lessons', 'voxel-media-integrator' ),
            __( 'Lessons', 'voxel-media-integrator' ),
            'manage_options',
            'edit.php?post_type=vmi_lessons',
            null
        );

        // Add New Lesson submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Add New Lesson', 'voxel-media-integrator' ),
            __( 'Add New Lesson', 'voxel-media-integrator' ),
            'manage_options',
            'post-new.php?post_type=vmi_lessons',
            null
        );

        // Add Quizzes submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Quizzes', 'voxel-media-integrator' ),
            __( 'Quizzes', 'voxel-media-integrator' ),
            'manage_options',
            'edit.php?post_type=vmi_quizzes',
            null
        );

        // Add New Quiz submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Add New Quiz', 'voxel-media-integrator' ),
            __( 'Add New Quiz', 'voxel-media-integrator' ),
            'manage_options',
            'post-new.php?post_type=vmi_quizzes',
            null
        );

        // Add Students submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Students', 'voxel-media-integrator' ),
            __( 'Students', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-students',
            array( $this, 'render_students_page' )
        );

        // Add Reports submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Reports', 'voxel-media-integrator' ),
            __( 'Reports', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-reports',
            array( $this, 'render_reports_page' )
        );

        // Add LMS Settings submenu
        add_submenu_page(
            'vmi-settings',
            __( 'LMS Settings', 'voxel-media-integrator' ),
            __( 'LMS Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-lms-settings',
            array( $this, 'render_lms_settings_page' )
        );

        // Add video dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Video Dashboard', 'voxel-media-integrator' ),
            __( 'Video Dashboard', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-video-dashboard', // Corrected slug
            array( $this, 'render_video_page' ) // Corrected callback
        );

        // Add video settings submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Video Settings', 'voxel-media-integrator' ),
            __( 'Video Settings', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-video-settings',
            array( $this, 'render_video_settings_page' )
        );

        // Add field setup submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Field Setup', 'voxel-media-integrator' ),
            __( 'Field Setup', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-field-setup',
            array( $this, 'render_field_setup_page' ) // Changed from render_field_setup_settings_page based on fixed file
        );

        // Add media dashboard submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Media Dashboard', 'voxel-media-integrator' ),
            __( 'Media Dashboard', 'voxel-media-integrator' ),
            'edit_posts',
            'vmi-media-dashboard',
            array( $this, 'render_media_dashboard_page' )
        );

        // Add templates submenu
        add_submenu_page(
            'vmi-settings',
            __( 'Templates', 'voxel-media-integrator' ),
            __( 'Templates', 'voxel-media-integrator' ),
            'manage_options',
            'vmi-templates',
            array( $this, 'render_templates_page' )
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register general settings
        register_setting( 'vmi_settings', 'vmi_media_limits', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_media_limits' ),
        ) );

        // Virtual Tours settings are now registered in the VMI_Virtual_Tours class

        // Register 3D models settings
        register_setting( 'vmi_3d_models_settings', 'vmi_3d_models_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_3d_models_settings' ),
        ) );

        // Register LMS settings
        register_setting( 'vmi_lms_settings', 'vmi_lms_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_lms_settings' ),
        ) );

        // Register video settings
        register_setting( 'vmi_video_settings', 'vmi_video_settings', array(
            'type' => 'array',
            'sanitize_callback' => array( $this, 'sanitize_video_settings' ),
        ) );

        // Add general settings section
        add_settings_section(
            'vmi_limits_section',
            __( 'Media Limits', 'voxel-media-integrator' ),
            array( $this, 'render_limits_section' ),
            'vmi_settings'
        );

        // Add settings fields
        add_settings_field(
            'vmi_free_limit',
            __( 'Free User Limit', 'voxel-media-integrator' ),
            array( $this, 'render_free_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );

        add_settings_field(
            'vmi_basic_limit',
            __( 'Basic Plan Limit', 'voxel-media-integrator' ),
            array( $this, 'render_basic_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );

        add_settings_field(
            'vmi_premium_limit',
            __( 'Premium Plan Limit', 'voxel-media-integrator' ),
            array( $this, 'render_premium_limit_field' ),
            'vmi_settings',
            'vmi_limits_section'
        );
    }

    // Virtual Tours settings sanitization is now handled by the VMI_Virtual_Tours class

    /**
     * Sanitize 3D models settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_3d_models_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'format_glb',
            'format_gltf',
            'format_obj',
            'format_fbx',
            'format_dae',
            'format_stl',
            'auto_rotate',
            'enable_timeline',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize numeric fields
        $numeric_fields = array(
            'default_model_height',
            'max_upload_size',
        );

        foreach ( $numeric_fields as $field ) {
            $output[ $field ] = isset( $input[ $field ] ) ? absint( $input[ $field ] ) : 0;
        }

        // Sanitize select fields
        $output['default_model_viewer'] = isset( $input['default_model_viewer'] ) && in_array( $input['default_model_viewer'], array( 'model-viewer', 'three' ) ) ? $input['default_model_viewer'] : 'model-viewer';

        return $output;
    }

    /**
     * Sanitize LMS settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_lms_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'enable_tutor_lms',
            'enable_categories',
            'enable_pricing',
            'enable_business',
            'enable_timeline',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize select fields
        $output['course_creation_role'] = isset( $input['course_creation_role'] ) && in_array( $input['course_creation_role'], array( 'administrator', 'editor', 'author', 'contributor', 'subscriber' ) ) ? $input['course_creation_role'] : 'administrator';
        $output['course_moderation'] = isset( $input['course_moderation'] ) && in_array( $input['course_moderation'], array( 'publish', 'pending' ) ) ? $input['course_moderation'] : 'pending';

        // Sanitize numeric fields
        $output['max_courses'] = isset( $input['max_courses'] ) ? absint( $input['max_courses'] ) : 0;

        // Sanitize text fields
        $output['business_field_id'] = isset( $input['business_field_id'] ) ? sanitize_text_field( $input['business_field_id'] ) : '';

        return $output;
    }

    /**
     * Sanitize video settings
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_video_settings( $input ) {
        $output = array();

        // Sanitize checkboxes
        $checkboxes = array(
            'enable_uploads',
            'enable_embedding',
            'format_mp4',
            'format_webm',
            'format_ogg',
            'format_mov',
            'embed_youtube',
            'embed_vimeo',
            'embed_dailymotion',
            'embed_facebook',
            'embed_twitch',
            'enable_business',
            'enable_timeline',
        );

        foreach ( $checkboxes as $checkbox ) {
            $output[ $checkbox ] = isset( $input[ $checkbox ] ) ? 1 : 0;
        }

        // Sanitize select fields
        $output['video_moderation'] = isset( $input['video_moderation'] ) && in_array( $input['video_moderation'], array( 'publish', 'pending' ) ) ? $input['video_moderation'] : 'pending';

        // Sanitize numeric fields
        $output['max_upload_size'] = isset( $input['max_upload_size'] ) ? absint( $input['max_upload_size'] ) : 0;
        $output['default_video_height'] = isset( $input['default_video_height'] ) ? absint( $input['default_video_height'] ) : 0;

        // Sanitize text fields
        $output['business_field_id'] = isset( $input['business_field_id'] ) ? sanitize_text_field( $input['business_field_id'] ) : '';

        return $output;
    }

    /**
     * Sanitize media limits
     *
     * @param array $input Input values.
     * @return array Sanitized values.
     */
    public function sanitize_media_limits( $input ) {
        $output = array();

        // Sanitize numeric fields
        $numeric_fields = array(
            'free_limit',
            'basic_limit',
            'premium_limit',
        );

        foreach ( $numeric_fields as $field ) {
            $output[ $field ] = isset( $input[ $field ] ) ? absint( $input[ $field ] ) : 0;
        }

        return $output;
    }

    /**
     * Render limits section
     */
    public function render_limits_section() {
        echo '<p>' . esc_html__( 'Set the media upload limits for different user plans.', 'voxel-media-integrator' ) . '</p>';
    }

    /**
     * Render free limit field
     */
    public function render_free_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $free_limit = isset( $options['free_limit'] ) ? $options['free_limit'] : 1;
        ?>
        <input type="number" name="vmi_media_limits[free_limit]" value="<?php echo esc_attr( $free_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for free users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Render basic limit field
     */
    public function render_basic_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $basic_limit = isset( $options['basic_limit'] ) ? $options['basic_limit'] : 5;
        ?>
        <input type="number" name="vmi_media_limits[basic_limit]" value="<?php echo esc_attr( $basic_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for basic plan users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Render premium limit field
     */
    public function render_premium_limit_field() {
        $options = get_option( 'vmi_media_limits', array() );
        $premium_limit = isset( $options['premium_limit'] ) ? $options['premium_limit'] : 20;
        ?>
        <input type="number" name="vmi_media_limits[premium_limit]" value="<?php echo esc_attr( $premium_limit ); ?>" min="0" />
        <p class="description"><?php esc_html_e( 'Maximum number of media items for premium plan users.', 'voxel-media-integrator' ); ?></p>
        <?php
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts() {
        wp_enqueue_style( 'vmi-admin-style', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/css/admin.css', array(), VMI_VERSION );
        wp_enqueue_script( 'vmi-admin-script', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/admin.js', array( 'jquery', 'wp-util' ), VMI_VERSION, true );

        // Add Chart.js for reports
        wp_enqueue_script( 'vmi-chart-js', plugin_dir_url( dirname( __FILE__ ) ) . 'assets/js/chart.min.js', array(), '3.7.0', true );

        // Add localization
        wp_localize_script( 'vmi-admin-script', 'vmiAdmin', array(
            'ajaxUrl' => admin_url( 'admin-ajax.php' ),
            'nonce' => wp_create_nonce( 'vmi-admin-nonce' ),
        ) );
    }

    /**
     * AJAX handler for dismissing field notices
     */
    public function dismiss_field_notice() {
        // Verify nonce
        check_ajax_referer( 'vmi_dismiss_field_notice_nonce', 'nonce' );

        // Update user meta
        update_user_meta( get_current_user_id(), 'vmi_dismissed_field_notice', true );

        wp_send_json_success();
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Voxel Media Integrator Settings', 'voxel-media-integrator' ); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields( 'vmi_settings' );
                do_settings_sections( 'vmi_settings' );
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render 3D Models settings page
     */
    public function render_3d_models_page() {
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( '3D Models Settings', 'voxel-media-integrator' ); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields( 'vmi_3d_models_settings' );
                // You will need to create sections and fields for these settings
                // Example: do_settings_sections( 'vmi_3d_models_settings' );
                ?>
                <p><?php esc_html_e('Settings for 3D models will be added here.', 'voxel-media-integrator'); ?></p>
                <?php
                submit_button();
                ?>
            </form>
        </div>
        <?php
        /*
         * Example structure (from class-admin-fixed.php, needs full implementation)
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( '3D Models Settings', 'voxel-media-integrator' ); ?></h1>
            <form method="post" action="options.php">
                <?php
                settings_fields( 'vmi_3d_models_settings' );
                do_settings_sections( 'vmi_3d_models_settings' ); // This assumes sections/fields are defined
                submit_button();
                ?>
            </form>
        </div>
        <?php
        */
    }

    /**
     * Render video page (dashboard)
     */
    public function render_video_page() {
        // Get counts
        $total_videos = wp_count_posts('vmi_videos');
        $published_videos = $total_videos->publish;
        $draft_videos = $total_videos->draft;
        $pending_videos = $total_videos->pending;

        // Get video types
        $youtube_videos = $this->count_posts_by_meta('vmi_videos', 'vmi_video_type', 'youtube');
        $vimeo_videos = $this->count_posts_by_meta('vmi_videos', 'vmi_video_type', 'vimeo');
        $self_hosted_videos = $published_videos - $youtube_videos - $vimeo_videos;

        // Get storage info
        $storage_used = $this->get_storage_usage('vmi_videos');
        $storage_available = 1073741824; // 1GB default limit

        // Get recent videos
        $recent_videos = get_posts(array(
            'post_type' => 'vmi_videos',
            'posts_per_page' => 5,
            'orderby' => 'date',
            'order' => 'DESC',
        ));

        // Get popular videos
        $popular_videos = get_posts(array(
            'post_type' => 'vmi_videos',
            'posts_per_page' => 5,
            'meta_key' => 'vmi_video_views',
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
        ));

        // Include template
        // Ensure this template path is correct or create the template file
        $template_path = VMI_PLUGIN_DIR . 'templates/admin/videos-dashboard.php';
        if ( file_exists( $template_path ) ) {
            require_once $template_path;
        } else {
            echo '<p>' . esc_html__( 'Video dashboard template not found.', 'voxel-media-integrator' ) . '</p>';
        }
    }

    /**
     * Render LMS page (dashboard)
     */
    public function render_lms_page() {
        // This is a placeholder. Implement LMS dashboard content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'LMS Dashboard', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'LMS dashboard content will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            /* Example from class-admin-fixed.php
            // Get counts
            $total_courses = wp_count_posts('vmi_courses')->publish;
            $total_lessons = wp_count_posts('vmi_lessons')->publish;
            $total_quizzes = wp_count_posts('vmi_quizzes')->publish;
            $total_students = count_users( array( 'role' => 'vmi_student' ) )['total_users'];

            // Get recent activity
            $recent_courses = get_posts(array('post_type' => 'vmi_courses', 'posts_per_page' => 3, 'orderby' => 'date', 'order' => 'DESC'));
            $recent_enrollments = []; // Placeholder for enrollment data

            // Include template
            require_once plugin_dir_path( dirname( __FILE__ ) ) . 'templates/admin/lms-dashboard.php';
            */
            ?>
        </div>
        <?php
    }

    /**
     * Render Video Settings page
     */
    public function render_video_settings_page() {
        // This is a placeholder. Implement video settings content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Video Settings', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Video settings content will be displayed here.', 'voxel-media-integrator' ); ?></p>
             <form method="post" action="options.php">
                <?php
                settings_fields( 'vmi_video_settings' );
                // You will need to create sections and fields for these settings
                // Example: do_settings_sections( 'vmi_video_settings' );
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Render Field Setup page
     */
    public function render_field_setup_page() {
        // This is a placeholder. Implement field setup content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Field Setup', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Field setup instructions and tools will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            /*
            // Based on class-admin-fixed.php
            if ( ! current_user_can( 'manage_options' ) ) {
                return;
            }

            $dismissed = get_user_meta( get_current_user_id(), 'vmi_dismissed_field_notice', true );

            ?>
            <div class="wrap vmi-field-setup">
                <h1><?php esc_html_e( 'Voxel Media Integrator - Field Setup Guide', 'voxel-media-integrator' ); ?></h1>

                <?php if ( ! $dismissed ) : ?>
                <div id="vmi-field-notice" class="notice notice-info is-dismissible">
                    <p>
                        <?php esc_html_e( 'Important: After creating custom fields in Voxel for your media types (Virtual Tours, 3D Models, Videos, LMS), you may need to come back here to map them if automatic detection fails, or to configure specific behaviors.', 'voxel-media-integrator' ); ?>
                    </p>
                    <button type="button" class="notice-dismiss"><span class="screen-reader-text"><?php esc_html_e( 'Dismiss this notice.', 'voxel-media-integrator' ); ?></span></button>
                </div>
                <?php endif; ?>

                <p><?php esc_html_e( 'This section will guide you on setting up Voxel custom fields for each media type supported by the plugin. For most media types, simply creating a field of the correct type (e.g., "URL" for 3D Vista links, "File" for 3D model uploads) within your Voxel post types (e.g., "Business") should be sufficient.', 'voxel-media-integrator' ); ?></p>

                <h2><?php esc_html_e( 'Media Types & Recommended Field Types:', 'voxel-media-integrator' ); ?></h2>
                <ul>
                    <li><strong><?php esc_html_e( 'Virtual Tours (3D Vista):', 'voxel-media-integrator' ); ?></strong> <?php esc_html_e( 'Use a "URL" or "Text" field in Voxel to store the 3D Vista share URL or embed code.', 'voxel-media-integrator' ); ?></li>
                    <li><strong><?php esc_html_e( '3D Models (GLB, FBX, etc.):', 'voxel-media-integrator' ); ?></strong> <?php esc_html_e( 'Use a "File" field in Voxel. Ensure allowed file types in Voxel settings include .glb, .fbx etc.', 'voxel-media-integrator' ); ?></li>
                    <li><strong><?php esc_html_e( 'Videos (MP4, Uploads):', 'voxel-media-integrator' ); ?></strong> <?php esc_html_e( 'Use a "File" field in Voxel. Ensure allowed file types include .mp4, .mov etc.', 'voxel-media-integrator' ); ?></li>
                    <li><strong><?php esc_html_e( 'Videos (Embeds - YouTube, Vimeo):', 'voxel-media-integrator' ); ?></strong> <?php esc_html_e( 'Use a "URL" or "oEmbed" field in Voxel.', 'voxel-media-integrator' ); ?></li>
                    <li><strong><?php esc_html_e( 'LMS Content (Tutor LMS):', 'voxel-media-integrator' ); ?></strong> <?php esc_html_e( 'Typically, this involves selecting a Tutor LMS Course ID. This might require a "Number" or "Post Relation" field if you are manually linking. The plugin aims to provide a more integrated experience via the user dashboard.', 'voxel-media-integrator' ); ?></li>
                </ul>

                <h3><?php esc_html_e( 'Automatic Detection & Manual Override:', 'voxel-media-integrator' ); ?></h3>
                <p><?php esc_html_e( 'The plugin will attempt to automatically detect appropriate fields based on their key or type. If automatic detection fails, or if you have multiple fields of the same type, a settings area will appear below allowing you to manually map field keys for each media type per Voxel post type.', 'voxel-media-integrator' ); ?></p>

                <?php
                // Placeholder: Logic to list post types and allow field mapping
                // This would involve querying Voxel post types and their fields
                // For now, this is a static guide.
                ?>
                <div id="vmi-field-mapping-area">
                    <!-- Field mapping UI will be loaded here if needed -->
                </div>
            </div>
            <script type="text/javascript">
                jQuery(document).ready(function($) {
                    $('#vmi-field-notice .notice-dismiss').on('click', function(e) {
                        e.preventDefault();
                        $.post(ajaxurl, {
                            action: 'vmi_dismiss_field_notice',
                            nonce: <?php echo json_encode( wp_create_nonce( 'vmi_dismiss_field_notice_nonce' ) ); ?>
                        });
                        $('#vmi-field-notice').hide();
                    });
                });
            </script>
            <?php
            */
            ?>
        </div>
        <?php
    }

    /**
     * Render Media Dashboard page
     */
    public function render_media_dashboard_page() {
        // This is a placeholder. Implement media dashboard content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Media Dashboard', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'User-facing media management dashboard content will be displayed here (likely via a shortcode or direct template include for users). This admin page is for preview or settings related to it.', 'voxel-media-integrator' ); ?></p>
            <?php
            /* Example from class-admin-fixed.php
            // Check if current user can manage their own media
            if ( ! current_user_can( 'upload_files' ) ) { // Or a more specific capability
                wp_die( esc_html__( 'You do not have permission to access this page.', 'voxel-media-integrator' ) );
            }

            $user_id = get_current_user_id();
            $media_types = array(
                'virtual_tours' => __('Virtual Tours', 'voxel-media-integrator'),
                '3d_models' => __('3D Models', 'voxel-media-integrator'),
                'videos' => __('Videos', 'voxel-media-integrator'),
                'lms_courses' => __('LMS Courses', 'voxel-media-integrator'),
            );

            // Get usage limits
            $limits_class = new VMI_Limits(); // Assuming VMI_Limits class is available
            $limits = $limits_class->get_user_limits( $user_id );

            ?>
            <div class="wrap vmi-media-dashboard">
                <h1><?php esc_html_e( 'My Media Dashboard', 'voxel-media-integrator' ); ?></h1>

                <div class="media-usage-overview">
                    <h2><?php esc_html_e( 'Media Usage & Limits', 'voxel-media-integrator' ); ?></h2>
                    <ul>
                        <?php foreach ( $limits as $type => $limit_data ) : ?>
                            <li>
                                <strong><?php echo esc_html( $media_types[$type] ?? ucfirst( str_replace('_', ' ', $type) ) ); ?>:</strong>
                                <?php echo esc_html( $limit_data['current'] ); ?> / <?php echo esc_html( $limit_data['limit'] == -1 ? 'Unlimited' : $limit_data['limit'] ); ?>
                                <?php if ( $limit_data['limit'] != -1 && $limit_data['current'] >= $limit_data['limit'] ) : ?>
                                    <span class="limit-reached"><?php esc_html_e( 'Limit Reached', 'voxel-media-integrator' ); ?></span>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <?php // Potentially add upgrade links here if integrated with a membership plugin ?>
                </div>

                <div class="media-management-sections">
                    <?php foreach ($media_types as $type_key => $type_label): ?>
                        <div class="media-section" id="vmi-media-<?php echo esc_attr($type_key); ?>">
                            <h2><?php echo esc_html($type_label); ?></h2>
                            <button class="button button-primary vmi-add-new-media" data-media-type="<?php echo esc_attr($type_key); ?>">
                                <?php printf(esc_html__('Add New %s', 'voxel-media-integrator'), esc_html($type_label)); ?>
                            </button>
                            <div class="vmi-media-list">
                                <?php // AJAX will load media items here ?>
                                <p><?php esc_html_e('Your media items will appear here.', 'voxel-media-integrator'); ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <?php // Modal for adding/editing media - this would be a more complex UI component ?>
                <div id="vmi-media-modal" style="display:none;">
                    </div>
            </div>
            <?php
            */
            ?>
        </div>
        <?php
    }

    /**
     * Render Templates page
     */
    public function render_templates_page() {
        // This is a placeholder. Implement templates management content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Media Templates', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Manage Elementor templates or shortcode templates for displaying media.', 'voxel-media-integrator' ); ?></p>
            <?php
            /* Example from class-admin-fixed.php
            global $vmi_templates; // Assuming $vmi_templates is an instance of VMI_Templates
            if ( ! isset( $vmi_templates ) || ! is_a( $vmi_templates, 'VMI_Templates' ) ) {
                // Attempt to instantiate if not available, though it should be loaded by main plugin
                if(class_exists('VMI_Templates')) {
                    $vmi_templates = new VMI_Templates();
                } else {
                    echo '<p>' . esc_html__( 'Templates class not found.', 'voxel-media-integrator' ) . '</p>';
                    return;
                }
            }

            $action = isset( $_GET['action'] ) ? sanitize_key( $_GET['action'] ) : 'list';
            $template_id = isset( $_GET['template_id'] ) ? absint( $_GET['template_id'] ) : 0;

            if ( $action === 'edit' && $template_id ) {
                // $vmi_templates->render_edit_template_page( $template_id );
                 echo '<p>' . esc_html__( 'Template editing UI placeholder.', 'voxel-media-integrator' ) . '</p>';
            } elseif ( $action === 'create' ) {
                // $vmi_templates->render_create_template_page();
                 echo '<p>' . esc_html__( 'Template creation UI placeholder.', 'voxel-media-integrator' ) . '</p>';
            } else {
                // $vmi_templates->render_list_templates_page();
                echo '<p>' . esc_html__( 'Template listing UI placeholder.', 'voxel-media-integrator' ) . '</p>';
            }
            ?>
             <div class="wrap vmi-templates-admin">
                <h1><?php esc_html_e( 'Media Display Templates', 'voxel-media-integrator' ); ?></h1>
                <a href="<?php echo esc_url( admin_url( 'admin.php?page=vmi-templates&action=create' ) ); ?>" class="page-title-action">
                    <?php esc_html_e( 'Add New Template', 'voxel-media-integrator' ); ?>
                </a>
                <?php
                // Example: List templates. This would typically use WP_List_Table
                $templates = get_posts(array('post_type' => 'vmi_template', 'posts_per_page' => -1));
                if ( $templates ) {
                    echo '<ul class="vmi-template-list">';
                    foreach ( $templates as $template ) {
                        echo '<li>';
                        echo esc_html( $template->post_title );
                        echo ' - <a href="' . esc_url( admin_url( 'admin.php?page=vmi-templates&action=edit&template_id=' . $template->ID ) ) . '">' . esc_html__('Edit', 'voxel-media-integrator') . '</a>';
                        // Add delete link with nonce
                        echo '</li>';
                    }
                    echo '</ul>';
                } else {
                    echo '<p>' . esc_html__( 'No templates found.', 'voxel-media-integrator' ) . '</p>';
                }
                ?>
            </div>
            <?php
            */
            ?>
        </div>
        <?php
    }


    /**
     * Render Students page
     */
    public function render_students_page() {
        // This is a placeholder. Implement students management content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Students', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Student management and enrollment information will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            /* Example from class-admin-fixed.php
            if ( ! class_exists( 'WP_List_Table' ) ) {
                require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
            }

            // Create a custom list table class for students if needed, or use WP_Users_List_Table
            // For simplicity, let's assume direct user query

            $args = array(
                'role'    => 'vmi_student', // Assuming a 'vmi_student' role
                'orderby' => 'user_nicename',
                'order'   => 'ASC'
            );
            $students = get_users( $args );

            ?>
            <div class="wrap">
                <h1 class="wp-heading-inline"><?php esc_html_e( 'Students', 'voxel-media-integrator' ); ?></h1>
                <?php // Add new student link if applicable: <a href="#" class="page-title-action">Add New Student</a> ?>

                <form method="get">
                    <input type="hidden" name="page" value="vmi-students" />
                    <?php // Add search box if needed: $user_search->search_box( __( 'Search Students' ), 'student' ); ?>
                </form>

                <table class="wp-list-table widefat fixed striped users">
                    <thead>
                    <tr>
                        <th scope="col" id="username" class="manage-column column-username column-primary sortable desc">
                            <a href="#"><span><?php esc_html_e( 'Username', 'voxel-media-integrator' ); ?></span><span class="sorting-indicator"></span></a>
                        </th>
                        <th scope="col" id="name" class="manage-column column-name sortable desc">
                            <a href="#"><span><?php esc_html_e( 'Name', 'voxel-media-integrator' ); ?></span><span class="sorting-indicator"></span></a>
                        </th>
                        <th scope="col" id="email" class="manage-column column-email sortable desc">
                            <a href="#"><span><?php esc_html_e( 'Email', 'voxel-media-integrator' ); ?></span><span class="sorting-indicator"></span></a>
                        </th>
                        <th scope="col" id="courses" class="manage-column column-courses">
                            <?php esc_html_e( 'Enrolled Courses', 'voxel-media-integrator' ); ?>
                        </th>
                    </tr>
                    </thead>
                    <tbody id="the-list" data-wp-lists="list:user">
                        <?php if ( ! empty( $students ) ) : ?>
                            <?php foreach ( $students as $student ) : ?>
                                <tr>
                                    <td class="username column-username has-row-actions column-primary" data-colname="Username">
                                        <?php echo get_avatar( $student->ID, 32 ); ?>
                                        <strong><a href="<?php echo esc_url( get_edit_user_link( $student->ID ) ); ?>"><?php echo esc_html( $student->user_login ); ?></a></strong>
                                        <br>
                                        <div class="row-actions">
                                            <span class="edit"><a href="<?php echo esc_url( get_edit_user_link( $student->ID ) ); ?>"><?php esc_html_e( 'Edit' ); ?></a> | </span>
                                            <span class="view"><a href="<?php echo esc_url( get_author_posts_url( $student->ID ) ); ?>"><?php esc_html_e( 'View Profile', 'voxel-media-integrator' ); ?></a></span>
                                        </div>
                                    </td>
                                    <td class="name column-name" data-colname="Name"><?php echo esc_html( $student->display_name ); ?></td>
                                    <td class="email column-email" data-colname="Email"><a href="mailto:<?php echo esc_attr( $student->user_email ); ?>"><?php echo esc_html( $student->user_email ); ?></a></td>
                                    <td class="courses column-courses" data-colname="Enrolled Courses">
                                        <?php
                                        // Placeholder: Logic to get and display enrolled courses for $student->ID
                                        echo esc_html__('N/A', 'voxel-media-integrator');
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else : ?>
                            <tr class="no-items"><td class="colspanchange" colspan="4"><?php esc_html_e( 'No students found.', 'voxel-media-integrator' ); ?></td></tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <?php
            */
            ?>
        </div>
        <?php
    }

    /**
     * Render Reports page
     */
    public function render_reports_page() {
        // This is a placeholder. Implement reports content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Reports', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Media usage reports and analytics will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            /* Example from class-admin-fixed.php
            ?>
            <div class="wrap vmi-reports">
                <h1><?php esc_html_e( 'Media Integrator - Reports', 'voxel-media-integrator' ); ?></h1>

                <div class="vmi-reports-grid">
                    <div class="vmi-report-card">
                        <h2><?php esc_html_e( 'Media Overview', 'voxel-media-integrator' ); ?></h2>
                        <canvas id="vmiMediaOverviewChart"></canvas>
                        <ul>
                            <li><?php printf( esc_html__( 'Total Virtual Tours: %d', 'voxel-media-integrator' ), wp_count_posts('vmi_virtual_tours')->publish ); ?></li>
                            <li><?php printf( esc_html__( 'Total 3D Models: %d', 'voxel-media-integrator' ), wp_count_posts('vmi_3d_models')->publish ); ?></li>
                            <li><?php printf( esc_html__( 'Total Videos: %d', 'voxel-media-integrator' ), wp_count_posts('vmi_videos')->publish ); ?></li>
                            <li><?php printf( esc_html__( 'Total LMS Courses: %d', 'voxel-media-integrator' ), wp_count_posts('vmi_courses')->publish ); ?></li>
                        </ul>
                    </div>

                    <div class="vmi-report-card">
                        <h2><?php esc_html_e( 'User Engagement (Placeholder)', 'voxel-media-integrator' ); ?></h2>
                        <canvas id="vmiUserEngagementChart"></canvas>
                        <p><?php esc_html_e( 'Data on views, shares, completions will be shown here.', 'voxel-media-integrator' ); ?></p>
                    </div>

                    <div class="vmi-report-card">
                        <h2><?php esc_html_e( 'Storage Usage', 'voxel-media-integrator' ); ?></h2>
                        <canvas id="vmiStorageUsageChart"></canvas>
                        <?php
                        $total_storage = 0;
                        $storage_details = '';
                        $media_post_types = ['vmi_virtual_tours', 'vmi_3d_models', 'vmi_videos']; // Add others as needed
                        foreach ($media_post_types as $pt) {
                            $usage = $this->get_storage_usage($pt);
                            $total_storage += $usage;
                            $storage_details .= '<li>' . esc_html(get_post_type_object($pt)->labels->name) . ': ' . size_format($usage) . '</li>';
                        }
                        ?>
                        <p><?php printf( esc_html__( 'Total Storage Used: %s', 'voxel-media-integrator' ), size_format($total_storage) ); ?></p>
                        <ul><?php echo $storage_details; // Sanitized within get_storage_usage and size_format ?></ul>
                    </div>

                    <div class="vmi-report-card">
                        <h2><?php esc_html_e( 'LMS Reports (Placeholder)', 'voxel-media-integrator' ); ?></h2>
                        <canvas id="vmiLmsReportsChart"></canvas>
                         <ul>
                            <li><?php printf( esc_html__( 'Total Students: %d', 'voxel-media-integrator' ), count_users( array( 'role' => 'vmi_student' ) )['total_users'] ); ?></li>
                            <li><?php printf( esc_html__( 'Completions This Month: %s', 'voxel-media-integrator' ), 'N/A' ); ?></li>
                        </ul>
                    </div>
                </div>

                <script type="text/javascript">
                jQuery(document).ready(function($) {
                    if (typeof Chart === 'undefined') return;

                    // Media Overview Chart (Doughnut)
                    var ctxMedia = document.getElementById('vmiMediaOverviewChart')?.getContext('2d');
                    if (ctxMedia) {
                        new Chart(ctxMedia, {
                            type: 'doughnut',
                            data: {
                                labels: [
                                    '<?php esc_js_e( "Virtual Tours", "voxel-media-integrator" ); ?>',
                                    '<?php esc_js_e( "3D Models", "voxel-media-integrator" ); ?>',
                                    '<?php esc_js_e( "Videos", "voxel-media-integrator" ); ?>',
                                    '<?php esc_js_e( "LMS Courses", "voxel-media-integrator" ); ?>'
                                ],
                                datasets: [{
                                    label: '<?php esc_js_e( "Media Types", "voxel-media-integrator" ); ?>',
                                    data: [
                                        <?php echo (int)wp_count_posts('vmi_virtual_tours')->publish; ?>,
                                        <?php echo (int)wp_count_posts('vmi_3d_models')->publish; ?>,
                                        <?php echo (int)wp_count_posts('vmi_videos')->publish; ?>,
                                        <?php echo (int)wp_count_posts('vmi_courses')->publish; ?>
                                    ],
                                    backgroundColor: [
                                        'rgba(255, 99, 132, 0.7)',
                                        'rgba(54, 162, 235, 0.7)',
                                        'rgba(255, 206, 86, 0.7)',
                                        'rgba(75, 192, 192, 0.7)'
                                    ],
                                    borderColor: [
                                        'rgba(255, 99, 132, 1)',
                                        'rgba(54, 162, 235, 1)',
                                        'rgba(255, 206, 86, 1)',
                                        'rgba(75, 192, 192, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: { responsive: true, maintainAspectRatio: false }
                        });
                    }

                    // Placeholder for other charts
                    /*
                    var ctxUserEngagement = document.getElementById('vmiUserEngagementChart')?.getContext('2d');
                    if(ctxUserEngagement) { /* Placeholder Chart */ /*}

                    var ctxStorage = document.getElementById('vmiStorageUsageChart')?.getContext('2d');
                    */
                     /* Example JavaScript code for storage chart:
                    if (ctxStorage) {
                        new Chart(ctxStorage, {
                            type: 'bar',
                            data: {
                                labels: [
                                    'Virtual Tours',
                                    '3D Models',
                                    'Videos'
                                ],
                                datasets: [{
                                    label: 'Storage Used (Bytes)',
                                    data: [
                                        0, // Virtual Tours storage
                                        0, // 3D Models storage
                                        0  // Videos storage
                                    ],
                                    backgroundColor: [
                                        'rgba(153, 102, 255, 0.7)',
                                        'rgba(255, 159, 64, 0.7)',
                                        'rgba(201, 203, 207, 0.7)'
                                    ],
                                    borderColor: [
                                        'rgba(153, 102, 255, 1)',
                                        'rgba(255, 159, 64, 1)',
                                        'rgba(201, 203, 207, 1)'
                                    ],
                                    borderWidth: 1
                                }]
                            },
                            options: { responsive: true, maintainAspectRatio: false, scales: { y: { beginAtZero: true } } }
                        });
                    }

                    // var ctxLms = document.getElementById('vmiLmsReportsChart')?.getContext('2d');
                    // if(ctxLms) { // Placeholder Chart }

                // End of JavaScript example
                </script>
            </div>
            <?php
            // End of example
            ?>
        </div>
        <?php
    }

    /**
     * Render LMS Settings page
     */
    public function render_lms_settings_page() {
        // This is a placeholder. Implement LMS settings content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'LMS Settings', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'LMS integration settings will be displayed here.', 'voxel-media-integrator' ); ?></p>
             <form method="post" action="options.php">
                <?php
                settings_fields( 'vmi_lms_settings' );
                 // You will need to create sections and fields for these settings
                // Example: do_settings_sections( 'vmi_lms_settings' );
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Count posts by meta key and value.
     * Helper function for dashboards.
     *
     * @param string $post_type Post type to query.
     * @param string $meta_key Meta key.
     * @param string $meta_value Meta value.
     * @return int Count of posts.
     */
    private function count_posts_by_meta( $post_type, $meta_key, $meta_value ) {
        $query = new WP_Query( array(
            'post_type'      => $post_type,
            'post_status'    => 'publish',
            'meta_key'       => $meta_key,
            'meta_value'     => $meta_value,
            'posts_per_page' => -1,
            'fields'         => 'ids', // Only get post IDs for performance
        ) );
        return $query->post_count;
    }

    /**
     * Get total storage usage for a post type by summing 'filesize' meta.
     * Helper function for dashboards.
     *
     * @param string $post_type Post type.
     * @return int Total size in bytes.
     */
    private function get_storage_usage( $post_type ) {
        global $wpdb;
        $total_size = 0;

        // This is a simplified example. Actual storage might be complex if files are not directly attached as post meta.
        // If files are in media library and linked, a different approach is needed.
        // Assuming 'vmi_filesize' meta key stores the size of an uploaded file for a CPT.
        $results = $wpdb->get_col( $wpdb->prepare(
            "SELECT pm.meta_value FROM {$wpdb->postmeta} pm
             JOIN {$wpdb->posts} p ON p.ID = pm.post_id
             WHERE p.post_type = %s AND pm.meta_key = %s AND p.post_status = 'publish'",
            $post_type,
            'vmi_filesize' // Example meta key for file size
        ) );

        if ( $results ) {
            foreach ( $results as $size ) {
                $total_size += absint( $size );
            }
        }
        return $total_size;
    }

    /**
     * Render Virtual Tours Dashboard page
     */
    public function render_virtual_tours_dashboard_page() {
        // This is a placeholder. Implement virtual tours dashboard content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Virtual Tours Dashboard', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Virtual tours overview and management will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            // Example:
            // $total_tours = wp_count_posts('vmi_virtual_tours')->publish;
            // echo '<p>Total Tours: ' . $total_tours . '</p>';
            // $recent_tours = get_posts(array('post_type' => 'vmi_virtual_tours', 'posts_per_page' => 5));
            // Display recent tours, stats, etc.
            // Include template: require_once VMI_PLUGIN_DIR . 'templates/admin/virtual-tours-dashboard.php';
            ?>
        </div>
        <?php
    }

    /**
     * Render Virtual Tours Settings page
     */
    public function render_virtual_tours_settings_page() {
        // This is a placeholder. Implement virtual tours settings content.
        ?>
        <div class="wrap">
            <h1><?php esc_html_e( 'Virtual Tour Settings', 'voxel-media-integrator' ); ?></h1>
            <p><?php esc_html_e( 'Settings specific to virtual tours will be displayed here.', 'voxel-media-integrator' ); ?></p>
            <?php
            // Example:
            // settings_fields( 'vmi_virtual_tours_settings' );
            // do_settings_sections( 'vmi_virtual_tours_settings' );
            // submit_button();
            ?>
        </div>
        <?php
    }
} // End of VMI_Admin class
?>